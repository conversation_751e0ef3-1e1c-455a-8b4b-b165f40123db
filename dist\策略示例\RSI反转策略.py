# RSI反转策略
# 基于RSI指标的超买超卖反转策略

signals = [0] * len(data)

# 参数设置
rsi_period = 14
oversold_level = 30    # 超卖线
overbought_level = 70  # 超买线

# 计算RSI指标
rsi = RSI(data['close'], rsi_period)

for i in range(len(data)):
    if pd.notna(rsi.iloc[i]):
        # RSI低于超卖线，买入信号
        if rsi.iloc[i] < oversold_level:
            signals[i] = 1
        # RSI高于超买线，卖出信号
        elif rsi.iloc[i] > overbought_level:
            signals[i] = -1
