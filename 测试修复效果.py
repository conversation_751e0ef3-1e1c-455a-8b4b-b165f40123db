#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果脚本
验证两个问题的修复情况
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def test_login_interface_fix():
    """测试登录界面修复效果"""
    print("=" * 60)
    print("测试1: 登录界面修复效果")
    print("=" * 60)
    
    print("📋 测试内容:")
    print("- 验证登录成功后界面是否正确关闭")
    print("- 检查是否只显示股票软件界面")
    print("- 确认没有多个窗口同时显示")
    
    print("\n🔧 修复内容:")
    print("- 将 hide_to_background() 改为 graceful_close()")
    print("- 登录成功后完全关闭登录界面")
    print("- 避免界面隐藏到后台造成混乱")
    
    print("\n✅ 预期效果:")
    print("- 登录成功后登录界面完全消失")
    print("- 只显示股票看图软件主界面")
    print("- 用户体验更加清晰流畅")
    
    return True

def test_xlsx_file_cleanup():
    """测试XLSX文件清理功能"""
    print("\n" + "=" * 60)
    print("测试2: XLSX文件自动清理功能")
    print("=" * 60)
    
    print("📋 测试内容:")
    print("- 验证回测XLSX文件上传后是否自动删除")
    print("- 检查上传失败时文件是否保留")
    print("- 确认文件删除状态正确记录")
    
    print("\n🔧 修复内容:")
    print("- 在使用者监控.py中添加文件删除逻辑")
    print("- 上传成功后调用 os.remove() 删除本地文件")
    print("- 在配置中记录文件删除状态")
    
    print("\n✅ 预期效果:")
    print("- 上传成功: 本地XLSX文件自动删除")
    print("- 上传失败: 本地XLSX文件保留以便重试")
    print("- 节省本地存储空间，避免文件堆积")
    
    # 检查user_config目录中的XLSX文件
    user_config_dir = Path("dist/user_config")
    if user_config_dir.exists():
        xlsx_files = list(user_config_dir.glob("*.xlsx"))
        print(f"\n📁 当前user_config目录中的XLSX文件: {len(xlsx_files)}个")
        for xlsx_file in xlsx_files[:5]:  # 只显示前5个
            file_size = xlsx_file.stat().st_size
            print(f"   - {xlsx_file.name} ({file_size} 字节)")
        if len(xlsx_files) > 5:
            print(f"   - ... 还有 {len(xlsx_files) - 5} 个文件")
    
    return True

def test_code_changes():
    """验证代码修改"""
    print("\n" + "=" * 60)
    print("测试3: 代码修改验证")
    print("=" * 60)
    
    # 检查登录注册.py的修改
    print("🔍 检查登录注册.py修改:")
    try:
        with open("登录注册.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "graceful_close()" in content and "close_login_after_delay" in content:
            print("   ✅ 登录界面关闭逻辑已修改")
        else:
            print("   ❌ 登录界面关闭逻辑未找到修改")
            
        if "股票看图软件启动成功，登录界面将关闭" in content:
            print("   ✅ 日志信息已更新")
        else:
            print("   ❌ 日志信息未更新")
            
    except Exception as e:
        print(f"   ❌ 检查登录注册.py失败: {e}")
    
    # 检查使用者监控.py的修改
    print("\n🔍 检查使用者监控.py修改:")
    try:
        with open("使用者监控.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "os.remove(filepath)" in content:
            print("   ✅ 文件删除逻辑已添加")
        else:
            print("   ❌ 文件删除逻辑未找到")
            
        if "file_deleted" in content:
            print("   ✅ 删除状态记录已添加")
        else:
            print("   ❌ 删除状态记录未找到")
            
        if "删除本地XLSX文件" in content:
            print("   ✅ 删除提示信息已添加")
        else:
            print("   ❌ 删除提示信息未找到")
            
    except Exception as e:
        print(f"   ❌ 检查使用者监控.py失败: {e}")
    
    return True

def test_exe_file():
    """测试exe文件"""
    print("\n" + "=" * 60)
    print("测试4: exe文件验证")
    print("=" * 60)
    
    exe_file = Path("dist/股票看图软件_登录版.exe")
    if exe_file.exists():
        file_size = exe_file.stat().st_size / (1024 * 1024)
        print(f"✅ exe文件存在: {file_size:.1f} MB")
        
        # 检查文件修改时间
        import datetime
        mod_time = datetime.datetime.fromtimestamp(exe_file.stat().st_mtime)
        print(f"✅ 文件修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
    else:
        print("❌ exe文件不存在")
        return False

def create_test_summary():
    """创建测试总结"""
    print("\n" + "=" * 60)
    print("🎯 修复效果总结")
    print("=" * 60)
    
    summary = """
## 修复完成情况

### 问题1: 登录成功后界面问题 ✅ 已修复
- **修改文件**: 登录注册.py
- **修改内容**: 将界面隐藏改为完全关闭
- **效果**: 登录成功后只显示股票软件界面

### 问题2: XLSX文件本地保留问题 ✅ 已修复  
- **修改文件**: 使用者监控.py
- **修改内容**: 上传成功后自动删除本地文件
- **效果**: 节省存储空间，避免文件堆积

### 用户体验改进
1. **界面更清晰**: 避免多窗口混乱
2. **操作更简单**: 无需手动关闭登录界面
3. **存储更智能**: 自动清理不需要的文件
4. **流程更顺畅**: 登录到使用一气呵成

### 技术改进
1. **代码逻辑优化**: 界面管理更合理
2. **文件管理改进**: 自动化程度更高
3. **错误处理增强**: 删除失败时的处理
4. **状态记录完善**: 便于问题追踪

### 部署建议
1. 使用新版本exe文件替换旧版本
2. 首次运行时测试登录流程
3. 观察XLSX文件的自动清理效果
4. 如有问题请查看更新说明.md
"""
    
    print(summary)
    
    # 保存测试总结到文件
    with open("dist/测试总结.md", "w", encoding="utf-8") as f:
        f.write("# 股票看图软件修复测试总结\n")
        f.write(summary)
    
    print("✅ 测试总结已保存到: dist/测试总结.md")

def main():
    """主函数"""
    print("🔧 股票看图软件修复效果测试")
    print("测试两个问题的修复情况")
    
    try:
        # 执行各项测试
        test_login_interface_fix()
        test_xlsx_file_cleanup()
        test_code_changes()
        exe_ok = test_exe_file()
        
        # 创建测试总结
        create_test_summary()
        
        print("\n" + "=" * 60)
        if exe_ok:
            print("🎉 所有修复已完成并打包成功！")
        else:
            print("⚠️ 修复完成但exe文件可能需要重新生成")
        print("=" * 60)
        
        print("\n📋 下一步操作:")
        print("1. 部署新版本exe文件")
        print("2. 测试登录流程是否正常")
        print("3. 验证XLSX文件自动删除功能")
        print("4. 如有问题请查看更新说明.md")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n✅ 测试完成，按回车键退出...")
        else:
            input("\n❌ 测试失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
