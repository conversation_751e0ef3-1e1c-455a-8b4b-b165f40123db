# 动量突破策略
# 基于价格动量和成交量的突破策略

signals = [0] * len(data)

# 参数设置
momentum_period = 10
roc_period = 12
volume_period = 20
momentum_threshold = 0.5
roc_threshold = 2.0
volume_multiplier = 1.5

# 计算指标
momentum = MOMENTUM(data['close'], momentum_period)
roc = ROC(data['close'], roc_period)
volume_ma = SMA(data['vol'], volume_period)

for i in range(max(momentum_period, roc_period, volume_period), len(data)):
    if (pd.notna(momentum.iloc[i]) and pd.notna(roc.iloc[i]) and 
        pd.notna(volume_ma.iloc[i])):
        
        # 买入条件：正动量 + 价格上涨 + 成交量放大
        if (momentum.iloc[i] > momentum_threshold and 
            roc.iloc[i] > roc_threshold and 
            data['vol'].iloc[i] > volume_ma.iloc[i] * volume_multiplier):
            signals[i] = 1
        
        # 卖出条件：负动量 + 价格下跌
        elif momentum.iloc[i] < -momentum_threshold and roc.iloc[i] < -roc_threshold:
            signals[i] = -1
