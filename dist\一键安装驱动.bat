@echo off
chcp 65001 >nul
echo ================================================
echo           浏览器驱动一键安装工具
echo ================================================
echo.
echo 此工具将为您安装以下浏览器驱动：
echo • Chrome 驱动 (chromedriver)
echo • Edge 驱动 (edgedriver)
echo • Firefox 驱动 (geckodriver)
echo.
echo 安装后，登录注册程序将不再需要联网下载驱动。
echo.
pause

echo 正在启动驱动安装器...
python "安装浏览器驱动.py"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败，可能的原因：
    echo 1. Python 未安装或未添加到 PATH
    echo 2. 缺少必要的 Python 库
    echo.
    echo 请尝试手动运行：python 安装浏览器驱动.py
    echo.
    pause
)

echo.
echo 安装完成！
pause
