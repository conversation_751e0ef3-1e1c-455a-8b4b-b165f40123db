# 多指标确认策略
# 需要多个指标同时确认才产生交易信号

signals = [0] * len(data)

# 计算各种指标
rsi = RSI(data['close'], 14)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)
upper, middle, lower = BOLL(data['close'], 20, 2)
k, d, j = KDJ(data['high'], data['low'], data['close'], 9)

for i in range(1, len(data)):
    if (pd.notna(rsi.iloc[i]) and pd.notna(dif.iloc[i]) and 
        pd.notna(dea.iloc[i]) and pd.notna(lower.iloc[i]) and
        pd.notna(k.iloc[i]) and pd.notna(d.iloc[i])):
        
        # 多重买入确认条件
        rsi_oversold = rsi.iloc[i] < 35
        macd_golden = (dif.iloc[i] > dea.iloc[i] and 
                      dif.iloc[i-1] <= dea.iloc[i-1])
        price_near_lower = data['close'].iloc[i] < lower.iloc[i] * 1.02
        kdj_oversold = k.iloc[i] < 25 and k.iloc[i] > d.iloc[i]
        
        # 多重卖出确认条件
        rsi_overbought = rsi.iloc[i] > 65
        macd_death = (dif.iloc[i] < dea.iloc[i] and 
                     dif.iloc[i-1] >= dea.iloc[i-1])
        price_near_upper = data['close'].iloc[i] > upper.iloc[i] * 0.98
        kdj_overbought = k.iloc[i] > 75 and k.iloc[i] < d.iloc[i]
        
        # 需要至少3个条件同时满足
        buy_signals = sum([rsi_oversold, macd_golden, price_near_lower, kdj_oversold])
        sell_signals = sum([rsi_overbought, macd_death, price_near_upper, kdj_overbought])
        
        if buy_signals >= 3:
            signals[i] = 1
        elif sell_signals >= 3:
            signals[i] = -1
