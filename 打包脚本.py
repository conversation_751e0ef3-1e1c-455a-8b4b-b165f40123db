#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票看图软件打包脚本
使用PyInstaller将Python程序打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('多股票监控配置.py', '.'),
    ('交易调度器.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
    ('drivers', 'drivers'),
    ('tushare_token.txt', '.'),
    ('multi_stock_config.json', '.'),
]

a = Analysis(
    ['登录注册.py'],  # 使用登录注册.py作为主入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础GUI库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',

        # 数据处理库
        'tushare',
        'pandas',
        'numpy',
        'openpyxl',
        'xlrd',
        'json',
        'pickle',
        'gzip',

        # 图表库
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.patches',
        'matplotlib.dates',
        'matplotlib.backends.backend_tkagg',

        # 网络和浏览器库
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        'requests',
        'urllib3',
        'lxml',
        'bs4',

        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',

        # 系统和工具库
        'datetime',
        'threading',
        'time',
        'os',
        'sys',
        'subprocess',
        'platform',
        'pathlib',
        'importlib',
        'importlib.util',
        'base64',
        'io',
        're',
        'signal',
        'traceback',
        'collections',
        'itertools',
        'functools',
        'warnings',

        # 自定义模块
        '股票看图软件_增强版',
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '多股票监控管理器',
        '多股票监控配置',
        '交易调度器',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        '浏览器驱动管理',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票看图软件_登录版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''

    with open('股票看图软件_登录版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✓ 已创建PyInstaller配置文件: 股票看图软件_登录版.spec")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")

    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "股票看图软件_登录版.spec"]

        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✓ exe文件构建成功！")
            print(f"输出目录: {os.path.abspath('dist')}")
            return True
        else:
            print("❌ exe文件构建失败")
            print("错误信息:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ 构建过程中出现异常: {str(e)}")
        return False

def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False

    # 需要复制的文件列表
    files_to_copy = [
        "requirements.txt",
        "README.md",
        "上传网址源代码.txt",
        "tushare_token.txt",
        "multi_stock_config.json",
        "一键安装驱动.bat",
    ]

    # 需要复制的目录
    dirs_to_copy = [
        "策略示例",
        "user_config",
        "market_data_cache",
        "drivers",
    ]

    print("正在复制额外文件...")

    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠ 复制文件失败 {file_name}: {str(e)}")

    # 复制目录
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            try:
                dest_dir = dist_dir / dir_name
                if dest_dir.exists():
                    shutil.rmtree(dest_dir)
                shutil.copytree(dir_name, dest_dir)
                print(f"✓ 已复制目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 复制目录失败 {dir_name}: {str(e)}")

    return True

def create_readme():
    """创建README文件"""
    readme_content = """# 股票看图软件 - 登录版

## 软件介绍
这是一个功能强大的股票分析和量化交易软件，集成了登录注册功能，包含以下主要功能：

### 主要功能
1. **登录注册系统**
   - Tushare账号注册和登录
   - Token自动获取和验证
   - 用户信息自动填写

2. **股票分析**
   - 支持MACD、KDJ、缠论等技术指标
   - 实时K线图显示
   - 十字光标和导航功能

3. **策略回测**
   - 内置多种经典策略
   - 自定义策略编写
   - 详细的回测分析报告

4. **多股票回测**
   - 批量回测多只股票
   - 策略效果对比分析
   - 最优参数寻找

5. **网页交易**
   - 自动化交易执行
   - 智能持仓管理
   - 基于策略信号的交易决策

6. **使用者监控**
   - 回测结果自动记录
   - 数据上传和分析
   - 用户行为监控

## 使用说明

### 首次使用
1. 双击运行 `股票看图软件_登录版.exe`
2. 在登录界面选择登录方式：
   - 如果已有Token，选择"Token登录"并输入Token
   - 如果没有账号，选择"注册"进行账号注册
   - 如果有账号但没有Token，选择"登录"获取Token
3. 登录成功后会自动启动股票看图软件

### 股票分析
1. 在股票分析页面输入股票代码（如：000001.SZ）
2. 点击"查询"获取股票数据
3. 选择技术指标进行分析
4. 使用十字光标查看详细数据

### 策略回测
1. 切换到"策略回测"页面
2. 设置回测参数（股票代码、日期范围、初始资金等）
3. 选择策略类型（MACD、KDJ或自定义）
4. 点击"运行回测"查看结果

### 网页交易
1. 切换到"网页交易"页面
2. 点击"连接交易网站"
3. 在浏览器中完成登录
4. 设置交易参数和仓位管理
5. 启用自动交易功能

## 注意事项
1. 首次运行可能需要较长时间加载
2. 网页交易功能需要安装Edge或Chrome浏览器
3. 请确保网络连接正常
4. 自动交易涉及真实资金，请谨慎使用
5. 软件会自动保存用户配置和缓存数据

## 文件说明
- `股票看图软件_登录版.exe`: 主程序文件
- `策略示例/`: 策略模板和示例
- `user_config/`: 用户配置和回测结果
- `market_data_cache/`: 市场数据缓存
- `drivers/`: 浏览器驱动文件

## 技术支持
如有问题请联系技术支持。

## 版本信息
版本: 登录版 v1.0
构建日期: """ + str(datetime.now().strftime("%Y-%m-%d")) + """
"""

    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("✓ 已创建README.md文件")

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    files_to_clean = ['*.pyc']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 清理目录失败 {dir_name}: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("股票看图软件 - 打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('登录注册.py'):
        print("❌ 未找到主程序文件: 登录注册.py")
        print("请确保在正确的目录中运行此脚本")
        return False

    if not os.path.exists('股票看图软件_增强版.py'):
        print("❌ 未找到股票软件文件: 股票看图软件_增强版.py")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建README文件
    create_readme()
    
    # 创建spec配置文件
    create_spec_file()
    
    # 构建exe文件
    if not build_exe():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print(f"exe文件位置: {os.path.abspath('dist/股票看图软件_登录版.exe')}")
    print(f"完整程序目录: {os.path.abspath('dist')}")
    print("\n使用说明:")
    print("1. 进入dist目录")
    print("2. 双击运行 股票看图软件_登录版.exe")
    print("3. 首次运行会显示登录注册界面")
    print("4. 登录成功后会自动启动股票看图软件")
    print("\n注意事项:")
    print("- 请确保目标电脑已安装Edge或Chrome浏览器")
    print("- 建议将整个dist目录复制到目标电脑")
    print("- 软件会自动创建配置文件和缓存目录")
    print("- 如遇到问题，请查看README.md文件")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
