#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包结果脚本
验证exe文件是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def test_exe_file():
    """测试exe文件"""
    print("=" * 60)
    print("测试打包结果")
    print("=" * 60)
    
    # 检查dist目录
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 检查exe文件
    exe_file = dist_dir / "股票看图软件_登录版.exe"
    if not exe_file.exists():
        print("❌ exe文件不存在")
        return False
    
    print(f"✓ 找到exe文件: {exe_file}")
    print(f"✓ 文件大小: {exe_file.stat().st_size / (1024*1024):.1f} MB")
    
    # 检查必要的文件和目录
    required_files = [
        "README.md",
        "tushare_token.txt",
        "multi_stock_config.json",
    ]
    
    required_dirs = [
        "策略示例",
        "user_config",
        "market_data_cache",
    ]
    
    print("\n检查必要文件:")
    for file_name in required_files:
        file_path = dist_dir / file_name
        if file_path.exists():
            print(f"✓ {file_name}")
        else:
            print(f"⚠ {file_name} (缺失)")
    
    print("\n检查必要目录:")
    for dir_name in required_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.rglob("*")))
            print(f"✓ {dir_name} ({file_count} 个文件)")
        else:
            print(f"⚠ {dir_name} (缺失)")
    
    # 尝试启动exe文件（非阻塞）
    print(f"\n尝试启动exe文件...")
    try:
        # 启动exe文件，但不等待它完成
        process = subprocess.Popen([str(exe_file)], 
                                 cwd=str(dist_dir),
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)
        
        print("✓ exe文件启动成功")
        print(f"✓ 进程ID: {process.pid}")
        
        # 等待几秒钟看是否立即崩溃
        time.sleep(3)
        
        if process.poll() is None:
            print("✓ exe文件运行正常（3秒后仍在运行）")
            
            # 尝试终止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✓ 成功终止测试进程")
            except:
                try:
                    process.kill()
                    print("✓ 强制终止测试进程")
                except:
                    print("⚠ 无法终止测试进程，可能需要手动关闭")
            
            return True
        else:
            print(f"❌ exe文件启动后立即退出，返回码: {process.returncode}")
            # 读取错误信息
            stdout, stderr = process.communicate()
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动exe文件失败: {str(e)}")
        return False

def create_usage_guide():
    """创建使用指南"""
    guide_content = """# 股票看图软件使用指南

## 打包完成！

您的股票看图软件已成功打包为单个exe文件。

## 文件说明

### 主程序
- `股票看图软件_登录版.exe` - 主程序文件，双击即可运行

### 配置文件
- `README.md` - 详细使用说明
- `tushare_token.txt` - Tushare API Token文件
- `multi_stock_config.json` - 多股票配置文件

### 数据目录
- `策略示例/` - 内置策略模板和示例
- `user_config/` - 用户配置和回测结果
- `market_data_cache/` - 市场数据缓存
- `drivers/` - 浏览器驱动文件

## 使用方法

1. **首次运行**
   - 双击 `股票看图软件_登录版.exe`
   - 会显示登录注册界面

2. **登录方式**
   - Token登录：如果已有Tushare Token，直接输入
   - 账号登录：输入手机号和密码获取Token
   - 新用户注册：完成注册流程获取Token

3. **功能使用**
   - 登录成功后自动启动股票看图软件
   - 支持股票分析、策略回测、网页交易等功能

## 部署说明

### 单机部署
- 将整个 `dist` 目录复制到目标电脑
- 确保目标电脑已安装Edge或Chrome浏览器
- 双击运行exe文件即可

### 注意事项
- 首次运行可能需要较长时间加载
- 软件会自动创建必要的配置文件
- 建议保留完整的目录结构

## 故障排除

### 常见问题
1. **程序无法启动**
   - 检查是否有杀毒软件拦截
   - 确保有足够的磁盘空间
   - 尝试以管理员身份运行

2. **浏览器相关错误**
   - 确保已安装Edge或Chrome浏览器
   - 检查浏览器驱动是否正常

3. **网络连接问题**
   - 确保网络连接正常
   - 检查防火墙设置

## 技术支持

如遇到问题，请查看README.md文件或联系技术支持。
"""
    
    with open("dist/使用指南.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✓ 已创建使用指南: dist/使用指南.md")

if __name__ == "__main__":
    try:
        success = test_exe_file()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 打包测试成功！")
            print("=" * 60)
            print("exe文件可以正常运行")
            
            # 创建使用指南
            create_usage_guide()
            
            print("\n部署说明:")
            print("1. 将整个 dist 目录复制到目标电脑")
            print("2. 双击运行 股票看图软件_登录版.exe")
            print("3. 按照界面提示完成登录或注册")
            print("4. 登录成功后会自动启动股票看图软件")
            
        else:
            print("\n" + "=" * 60)
            print("❌ 打包测试失败")
            print("=" * 60)
            print("exe文件可能存在问题，请检查打包过程")
            
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
