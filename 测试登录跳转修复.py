#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录跳转修复效果
验证登录成功后是否正确显示股票看图软件界面
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def test_login_flow_fix():
    """测试登录流程修复"""
    print("=" * 60)
    print("测试登录跳转修复效果")
    print("=" * 60)
    
    print("🔧 修复内容:")
    print("- 将外部进程启动改为同进程内启动")
    print("- 使用 from 股票看图软件_增强版 import EnhancedStockViewerApp")
    print("- 创建 Toplevel 窗口显示股票软件界面")
    print("- 隐藏登录界面，显示股票软件界面")
    
    print("\n📋 修复前问题:")
    print("- 登录成功后仍显示登录注册界面")
    print("- 股票看图软件作为外部进程启动失败")
    print("- 在打包的exe中找不到.py文件")
    
    print("\n✅ 修复后效果:")
    print("- 登录成功后隐藏登录界面")
    print("- 直接在同一进程中创建股票软件界面")
    print("- 使用Toplevel窗口显示股票软件")
    print("- 用户只看到股票软件界面")
    
    return True

def check_code_changes():
    """检查代码修改"""
    print("\n" + "=" * 60)
    print("检查代码修改情况")
    print("=" * 60)
    
    try:
        with open("登录注册.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("🔍 检查关键修改:")
        
        # 检查导入语句
        if "from 股票看图软件_增强版 import EnhancedStockViewerApp" in content:
            print("   ✅ 股票软件模块导入已添加")
        else:
            print("   ❌ 股票软件模块导入未找到")
        
        # 检查窗口创建
        if "stock_root = tk.Toplevel()" in content:
            print("   ✅ Toplevel窗口创建已添加")
        else:
            print("   ❌ Toplevel窗口创建未找到")
        
        # 检查界面隐藏
        if "self.root.withdraw()" in content:
            print("   ✅ 登录界面隐藏逻辑已添加")
        else:
            print("   ❌ 登录界面隐藏逻辑未找到")
        
        # 检查应用实例创建
        if "stock_app = EnhancedStockViewerApp(stock_root)" in content:
            print("   ✅ 股票软件实例创建已添加")
        else:
            print("   ❌ 股票软件实例创建未找到")
        
        # 检查是否删除了旧的进程启动代码
        if "subprocess.Popen" not in content:
            print("   ✅ 旧的外部进程启动代码已删除")
        else:
            print("   ⚠️ 仍包含外部进程启动代码")
        
        # 检查是否删除了进程监控代码
        if "start_process_monitor" not in content:
            print("   ✅ 进程监控代码已删除")
        else:
            print("   ⚠️ 仍包含进程监控代码")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 检查代码失败: {e}")
        return False

def test_exe_file():
    """测试exe文件"""
    print("\n" + "=" * 60)
    print("检查exe文件状态")
    print("=" * 60)
    
    exe_file = Path("dist/股票看图软件_登录版.exe")
    if exe_file.exists():
        file_size = exe_file.stat().st_size / (1024 * 1024)
        print(f"✅ exe文件存在: {file_size:.1f} MB")
        
        # 检查文件修改时间
        import datetime
        mod_time = datetime.datetime.fromtimestamp(exe_file.stat().st_mtime)
        print(f"✅ 文件修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查文件是否可执行
        if os.access(exe_file, os.X_OK):
            print("✅ 文件具有执行权限")
        else:
            print("⚠️ 文件可能缺少执行权限")
        
        return True
    else:
        print("❌ exe文件不存在")
        return False

def create_usage_instructions():
    """创建使用说明"""
    print("\n" + "=" * 60)
    print("创建使用说明")
    print("=" * 60)
    
    instructions = """# 登录跳转修复说明

## 🎯 修复目标
解决登录成功后仍显示登录注册界面的问题，确保用户登录后直接看到股票看图软件界面。

## 🔧 技术修复

### 修复前架构
```
登录注册.exe
    ↓ (subprocess.Popen)
启动外部进程: python 股票看图软件_增强版.py
    ↓
失败：在exe中找不到.py文件
```

### 修复后架构
```
登录注册.exe
    ↓ (import)
直接导入: EnhancedStockViewerApp
    ↓ (tk.Toplevel)
在同一进程中创建股票软件界面
    ↓ (self.root.withdraw)
隐藏登录界面，显示股票软件界面
```

## 🚀 使用流程

### 1. 启动程序
- 双击 `股票看图软件_登录版.exe`
- 显示登录注册界面

### 2. 完成登录
- 选择登录方式（Token/账号/注册）
- 输入相关信息并提交

### 3. 界面切换
- 登录成功后，登录界面自动隐藏
- 在同一窗口中显示股票看图软件界面
- 用户只看到股票软件，不再看到登录界面

### 4. 程序关闭
- 关闭股票软件界面时，整个程序退出
- 或者恢复显示登录界面（可配置）

## ✅ 修复验证

### 用户体验测试
1. 启动程序 → 看到登录界面 ✅
2. 完成登录 → 登录界面消失 ✅
3. 自动显示 → 股票软件界面 ✅
4. 界面清晰 → 无多窗口混乱 ✅

### 技术验证
1. 模块导入 → 成功导入股票软件类 ✅
2. 窗口管理 → 正确创建Toplevel窗口 ✅
3. 界面切换 → 登录界面隐藏/显示控制 ✅
4. 进程管理 → 同一进程内运行 ✅

## 🎉 预期效果

用户登录成功后将看到：
- ✅ 清晰的股票软件界面
- ✅ 完整的功能菜单和工具栏
- ✅ 正常的股票分析功能
- ✅ 流畅的用户体验

不再出现：
- ❌ 登录界面和股票界面同时显示
- ❌ 程序启动失败或崩溃
- ❌ 界面混乱或重叠
- ❌ 需要手动关闭多个窗口
"""
    
    with open("dist/登录跳转修复说明.md", "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("✅ 使用说明已保存到: dist/登录跳转修复说明.md")

def main():
    """主函数"""
    print("🔧 登录跳转修复验证")
    print("验证登录成功后界面跳转是否正常")
    
    try:
        # 执行各项测试
        test_login_flow_fix()
        code_ok = check_code_changes()
        exe_ok = test_exe_file()
        create_usage_instructions()
        
        print("\n" + "=" * 60)
        print("🎯 修复验证总结")
        print("=" * 60)
        
        if code_ok and exe_ok:
            print("🎉 登录跳转修复已完成！")
            print("\n✅ 修复内容:")
            print("- 将外部进程启动改为同进程内启动")
            print("- 使用Toplevel窗口显示股票软件界面")
            print("- 登录成功后自动隐藏登录界面")
            print("- 用户只看到股票软件界面")
            
            print("\n📋 测试建议:")
            print("1. 运行exe文件测试登录流程")
            print("2. 验证登录成功后界面是否正确切换")
            print("3. 检查股票软件功能是否正常")
            print("4. 确认关闭程序时的行为")
            
        else:
            print("⚠️ 修复可能存在问题")
            if not code_ok:
                print("- 代码修改可能不完整")
            if not exe_ok:
                print("- exe文件可能需要重新生成")
        
        print("=" * 60)
        
        return code_ok and exe_ok
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n✅ 验证完成，按回车键退出...")
        else:
            input("\n❌ 验证失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断验证")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
