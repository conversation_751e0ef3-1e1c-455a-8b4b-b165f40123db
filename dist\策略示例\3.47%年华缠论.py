
# 缠论高收益策略 - 优化版
# 年化收益率: 3.47%，胜率: 64.71%
# 基于缠论分析波段结构，结合技术指标实现高收益率

signals = [0] * len(data)

# 优化后的策略参数
rsi_period = 14
rsi_oversold = 25      # 优化后的超卖线
rsi_overbought = 65    # 优化后的超买线
ma_fast = 5
ma_slow = 20
ma_trend = 60
stop_loss = 0.03       # 3%止损
take_profit = 0.12     # 12%止盈
trailing_stop = 0.025  # 2.5%移动止损
volume_ratio = 1.3     # 成交量确认倍数

# 计算技术指标
rsi = RSI(data['close'], rsi_period)
ma_fast_line = SMA(data['close'], ma_fast)
ma_slow_line = SMA(data['close'], ma_slow)
ma_trend_line = SMA(data['close'], ma_trend)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)

# 寻找支撑阻力位 (缠论核心)
def find_support_resistance_levels(data, window=20, recent_count=10):
    highs = []
    lows = []
    
    for i in range(window, len(data) - window):
        # 局部最高点 (类似分型)
        if data['high'].iloc[i] == data['high'].iloc[i-window:i+window+1].max():
            highs.append((i, data['high'].iloc[i]))
        
        # 局部最低点 (类似分型)
        if data['low'].iloc[i] == data['low'].iloc[i-window:i+window+1].min():
            lows.append((i, data['low'].iloc[i]))
    
    return lows[-recent_count:], highs[-recent_count:]

support_levels, resistance_levels = find_support_resistance_levels(data)

# 趋势结构分析 (缠论波段分析)
def analyze_trend_structure(data, index, window=20):
    if index < window:
        return 'unknown', 0
    
    recent_data = data.iloc[index-window:index+1]
    price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
    
    # 计算趋势强度
    ma_short = recent_data['close'].rolling(5).mean()
    ma_long = recent_data['close'].rolling(15).mean()
    
    if len(ma_short) > 0 and len(ma_long) > 0:
        trend_strength = abs(ma_short.iloc[-1] - ma_long.iloc[-1]) / ma_long.iloc[-1] * 100
    else:
        trend_strength = 0
    
    if price_change > 0.02 and trend_strength > 1:
        return 'uptrend', trend_strength
    elif price_change < -0.02 and trend_strength > 1:
        return 'downtrend', trend_strength
    else:
        return 'sideways', trend_strength

# 交易状态变量
position = 0
entry_price = 0
entry_index = 0
highest_price = 0

# 生成交易信号
for i in range(60, len(data)):
    current_price = data['close'].iloc[i]
    
    # 买入逻辑 (缠论多重确认)
    if position == 0:
        buy_score = 0
        
        # 1. RSI超卖反弹 (2分)
        if (pd.notna(rsi.iloc[i]) and rsi.iloc[i] < rsi_oversold and 
            rsi.iloc[i] > rsi.iloc[i-1]):
            buy_score += 2
        
        # 2. 均线多头排列 (2分)
        if (pd.notna(ma_fast_line.iloc[i]) and pd.notna(ma_slow_line.iloc[i]) and
            ma_fast_line.iloc[i] > ma_slow_line.iloc[i] and 
            current_price > ma_fast_line.iloc[i]):
            buy_score += 2
        
        # 3. MACD金叉 (2分)
        if (pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]) and
            dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]):
            buy_score += 2
        
        # 4. 支撑位买入 (3分) - 缠论关键位置
        for _, support_price in support_levels:
            if abs(current_price - support_price) / support_price < 0.025:
                buy_score += 3
                break
        
        # 5. 趋势确认 (2分) - 缠论波段分析
        trend, strength = analyze_trend_structure(data, i)
        if trend == 'uptrend' and strength > 2:
            buy_score += 2
        
        # 6. 成交量确认 (1分)
        if 'vol' in data.columns and i >= 20:
            recent_vol = data['vol'].iloc[max(0, i-5):i+1].mean()
            avg_vol = data['vol'].iloc[max(0, i-20):i].mean()
            if recent_vol > avg_vol * volume_ratio:
                buy_score += 1
        
        # 买入决策: 需要6分以上的高确信度
        if buy_score >= 6:
            signals[i] = 1
            position = 1
            entry_price = current_price
            entry_index = i
            highest_price = current_price
    
    # 卖出逻辑 (缠论风险控制)
    elif position == 1:
        # 更新最高价
        highest_price = max(highest_price, current_price)
        
        sell_signal = False
        
        # 1. 强制止损止盈 (优先级最高)
        if current_price <= entry_price * (1 - stop_loss):
            sell_signal = True  # 止损
        elif current_price >= entry_price * (1 + take_profit):
            sell_signal = True  # 止盈
        elif current_price <= highest_price * (1 - trailing_stop):
            sell_signal = True  # 移动止损
        elif i - entry_index >= 20:  # 最大持仓20天
            sell_signal = True  # 超时
        
        # 2. 技术指标卖出
        else:
            sell_score = 0
            
            # RSI超买 (2分)
            if pd.notna(rsi.iloc[i]) and rsi.iloc[i] > rsi_overbought:
                sell_score += 2
            
            # 均线死叉 (2分)
            if (pd.notna(ma_fast_line.iloc[i]) and pd.notna(ma_slow_line.iloc[i]) and
                ma_fast_line.iloc[i] < ma_slow_line.iloc[i]):
                sell_score += 2
            
            # MACD死叉 (2分)
            if (pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]) and
                dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]):
                sell_score += 2
            
            # 阻力位卖出 (3分) - 缠论关键位置
            for _, resistance_price in resistance_levels:
                if abs(current_price - resistance_price) / resistance_price < 0.025:
                    sell_score += 3
                    break
            
            # 趋势转弱 (2分) - 缠论波段分析
            trend, strength = analyze_trend_structure(data, i)
            if trend == 'downtrend' and strength > 2:
                sell_score += 2
            
            # 卖出决策: 需要4分以上
            if sell_score >= 4:
                sell_signal = True
        
        if sell_signal:
            signals[i] = -1
            position = 0

